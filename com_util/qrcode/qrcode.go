package qrcode

import (
	"bytes"
	"image"
	"image/png"
	"taoqigame/com_err"

	"github.com/skip2/go-qrcode"
)

// GenerateQRCode 生成二维码图片
func GenerateQRCode(content string, size int) ([]byte, error) {
	if size <= 0 {
		size = 200 // 默认尺寸
	}

	// 生成二维码
	qrCode, err := qrcode.Encode(content, qrcode.Medium, size)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "生成二维码失败", err)
	}

	return qrCode, nil
}

// GenerateQRCodeImage 生成二维码图片并返回image.Image
func GenerateQRCodeImage(content string, size int) (image.Image, error) {
	if size <= 0 {
		size = 200 // 默认尺寸
	}

	// 生成二维码
	qrCode, err := qrcode.Encode(content, qrcode.Medium, size)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "生成二维码失败", err)
	}

	// 解码为image.Image
	img, err := png.Decode(bytes.NewReader(qrCode))
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "解码二维码图片失败", err)
	}

	return img, nil
}
